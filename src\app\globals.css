@tailwind base;
@tailwind components;
@tailwind utilities;
@plugin "@tailwindcss/forms";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
	--animate-hide: hide 150ms cubic-bezier(0.16, 1, 0.3, 1);
	--animate-slide-down-and-fade: slideDownAndFade 150ms
		cubic-bezier(0.16, 1, 0.3, 1);
	--animate-slide-left-and-fade: slideLeftAndFade 150ms
		cubic-bezier(0.16, 1, 0.3, 1);
	--animate-slide-up-and-fade: slideUpAndFade 150ms
		cubic-bezier(0.16, 1, 0.3, 1);
	--animate-slide-right-and-fade: slideRightAndFade 150ms
		cubic-bezier(0.16, 1, 0.3, 1);
	--animate-accordion-open: accordionOpen 150ms
		cubic-bezier(0.87, 0, 0.13, 1);
	--animate-accordion-close: accordionClose 150ms
		cubic-bezier(0.87, 0, 0.13, 1);
	--animate-dialog-overlay-show: dialogOverlayShow 150ms
		cubic-bezier(0.16, 1, 0.3, 1);
	--animate-dialog-content-show: dialogContentShow 150ms
		cubic-bezier(0.16, 1, 0.3, 1);
	--animate-drawer-slide-left-and-fade: drawerSlideLeftAndFade 150ms
		cubic-bezier(0.16, 1, 0.3, 1);
	--animate-drawer-slide-right-and-fade: drawerSlideRightAndFade 150ms
		ease-in;

	@keyframes hide {
		from {
			opacity: 1;
		}
		to {
			opacity: 0;
		}
	}
	@keyframes slideDownAndFade {
		from {
			opacity: 0;
			transform: translateY(-6px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	@keyframes slideLeftAndFade {
		from {
			opacity: 0;
			transform: translateX(6px);
		}
		to {
			opacity: 1;
			transform: translateX(0);
		}
	}
	@keyframes slideUpAndFade {
		from {
			opacity: 0;
			transform: translateY(6px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	@keyframes slideRightAndFade {
		from {
			opacity: 0;
			transform: translateX(-6px);
		}
		to {
			opacity: 1;
			transform: translateX(0);
		}
	}
	@keyframes accordionOpen {
		from {
			height: 0px;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordionClose {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0px;
		}
	}
	@keyframes dialogOverlayShow {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
	@keyframes dialogContentShow {
		from {
			opacity: 0;
			transform: translate(-50%, -45%) scale(0.95);
		}
		to {
			opacity: 1;
			transform: translate(-50%, -50%) scale(1);
		}
	}
	@keyframes drawerSlideLeftAndFade {
		from {
			opacity: 0;
			transform: translateX(100%);
		}
		to {
			opacity: 1;
			transform: translateX(0);
		}
	}
	@keyframes drawerSlideRightAndFade {
		from {
			opacity: 1;
			transform: translateX(0);
		}
		to {
			opacity: 0;
			transform: translateX(100%);
		}
	}
}
